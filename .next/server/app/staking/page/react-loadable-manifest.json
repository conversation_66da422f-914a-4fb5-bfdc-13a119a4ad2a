{"[project]/node_modules/.pnpm/@rainbow-me+rainbowkit@2.2.8_@tanstack+react-query@5.81.5_react@19.1.0__@types+react@19_94ea021389e2422cd5c357c23ae1309e/node_modules/@rainbow-me/rainbowkit/dist/index.js [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/node_modules/.pnpm/@rainbow-me+rainbowkit@2.2.8_@tanstack+react-query@5.81.5_react@19.1.0__@types+react@19_94ea021389e2422cd5c357c23ae1309e/node_modules/@rainbow-me/rainbowkit/dist/index.js [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/node_modules__pnpm_a6d57063._.js", "static/chunks/28c9a_@rainbow-me_rainbowkit_dist_cab0c322._.js", "static/chunks/b936f_viem__esm_2ea35737._.js", "static/chunks/42671_@wagmi_core_dist_esm_b0c70439._.js", "static/chunks/4e570_@noble_curves_esm_553f2431._.js", "static/chunks/fe147_ox__esm_core_5a8b2924._.js", "static/chunks/node_modules__pnpm_a5cb0c71._.js", "static/chunks/28c9a_@rainbow-me_rainbowkit_dist_index_3cd44a3d.js"]}, "[project]/src/components/WalletProviders.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/src/components/WalletProviders.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/node_modules__pnpm_fab61686._.js", "static/chunks/28c9a_@rainbow-me_rainbowkit_dist_be767256._.js", "static/chunks/b936f_viem__esm_16502704._.js", "static/chunks/42671_@wagmi_core_dist_esm_b0c70439._.js", "static/chunks/4e570_@noble_curves_esm_553f2431._.js", "static/chunks/fe147_ox__esm_core_5a8b2924._.js", "static/chunks/node_modules__pnpm_361ee4f1._.js", "static/chunks/src_40aaa11c._.js", "static/chunks/28c9a_@rainbow-me_rainbowkit_dist_index_58ff6d59.css", "static/chunks/src_components_WalletProviders_tsx_193bb971._.js"]}, "[project]/src/contexts/WalletAuthContext.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/src/contexts/WalletAuthContext.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/src_contexts_WalletAuthContext_tsx_13a234b1._.js", "static/chunks/src_contexts_WalletAuthContext_tsx_193bb971._.js"]}}