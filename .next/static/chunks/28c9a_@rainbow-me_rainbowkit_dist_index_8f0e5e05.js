(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@rainbow-me+rainbowkit@2.2.8_@tanstack+react-query@5.81.5_react@19.1.0__@types+react@19_94ea021389e2422cd5c357c23ae1309e/node_modules/@rainbow-me/rainbowkit/dist/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_bd289fbc._.js",
  "static/chunks/28c9a_@rainbow-me_rainbowkit_dist_cab0c322._.js",
  "static/chunks/b936f_viem__esm_2ea35737._.js",
  "static/chunks/42671_@wagmi_core_dist_esm_b0c70439._.js",
  "static/chunks/4e570_@noble_curves_esm_553f2431._.js",
  "static/chunks/fe147_ox__esm_core_5a8b2924._.js",
  "static/chunks/node_modules__pnpm_a5cb0c71._.js",
  "static/chunks/28c9a_@rainbow-me_rainbowkit_dist_index_b7a06ecd.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@rainbow-me+rainbowkit@2.2.8_@tanstack+react-query@5.81.5_react@19.1.0__@types+react@19_94ea021389e2422cd5c357c23ae1309e/node_modules/@rainbow-me/rainbowkit/dist/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);