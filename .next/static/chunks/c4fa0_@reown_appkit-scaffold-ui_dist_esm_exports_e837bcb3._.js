(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@reown+appkit-scaffold-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_types_a0114d14a85d18ae20a240b4a633f8e7/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/basic.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_bbc3d902._.js",
  "static/chunks/node_modules__pnpm_4107066a._.js",
  "static/chunks/c4fa0_@reown_appkit-scaffold-ui_dist_esm_exports_basic_10f777f0.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-scaffold-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_types_a0114d14a85d18ae20a240b4a633f8e7/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/basic.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-scaffold-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_types_a0114d14a85d18ae20a240b4a633f8e7/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_c4f40d03._.js",
  "static/chunks/node_modules__pnpm_1e0ad2d3._.js",
  "static/chunks/c4fa0_@reown_appkit-scaffold-ui_dist_esm_exports_w3m-modal_10f777f0.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-scaffold-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_types_a0114d14a85d18ae20a240b4a633f8e7/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-client] (ecmascript)");
    });
});
}}),
}]);