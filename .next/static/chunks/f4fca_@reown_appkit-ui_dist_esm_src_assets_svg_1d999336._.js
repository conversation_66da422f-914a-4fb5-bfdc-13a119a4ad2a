(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_add_6602c04e.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_add_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_all-wallets_d2dc6d5b.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_all-wallets_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom-circle_dc973fcd.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom-circle_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_app-store_b7d20553.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_app-store_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_apple_14bac9fd.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_apple_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom_583010f8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-left.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-left_c56054d4.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-left_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-left.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-right.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-right_07d47053.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-right_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-right.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-top.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-top_635c36e8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-top_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-top.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bank.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_bank_1cf46ded.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_bank_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bank.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_browser_c6390ee8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_browser_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/card.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_card_8a9ad507.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_card_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/card.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark_26df68e8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark-bold_faa81e10.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark-bold_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-bottom.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-bottom_c10bd9d0.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-bottom_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-bottom.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-left_9a559d0c.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-left_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-right.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-right_d1570115.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-right_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-right.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-top.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-top_78fdd661.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-top_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-top.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chrome-store_33163e22.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_chrome-store_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_clock_3a121d2c.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_clock_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_close_2b3028f8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_close_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/compass.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_compass_adf0049e.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_compass_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/compass.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/coinPlaceholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_coinPlaceholder_1158ef87.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_coinPlaceholder_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/coinPlaceholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/copy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_copy_3c3e9868.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_copy_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/copy.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_cursor_8a9ef69b.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_cursor_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_cursor-transparent_0eac256d.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_cursor-transparent_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/desktop.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_desktop_0f3d65cd.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_desktop_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/desktop.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/disconnect.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_disconnect_8da0b7ec.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_disconnect_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/disconnect.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_discord_8edfe6b7.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_discord_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_etherscan_c31b9c16.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_etherscan_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/extension.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_extension_11b04cb4.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_extension_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/extension.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/external-link.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_external-link_0eb418ae.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_external-link_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/external-link.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_facebook_2c78fbc8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_facebook_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_farcaster_56403d0a.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_farcaster_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_filters_f794b9bd.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_filters_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_github_83388d35.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_github_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_google_8aec3eca.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_google_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/help-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_help-circle_27e3f883.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_help-circle_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/help-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/image.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_image_d528b228.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_image_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/image.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/id.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_id_f61f11c7.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_id_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/id.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_info-circle_42470c45.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_info-circle_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/lightbulb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_lightbulb_6f24111a.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_lightbulb_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/lightbulb.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mail.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_mail_d52dabaa.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_mail_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mail.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_mobile_a214b53f.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_mobile_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/more.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_more_781b6865.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_more_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/more.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_network-placeholder_04a7bb7a.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_network-placeholder_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/nftPlaceholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_nftPlaceholder_98404ebc.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_nftPlaceholder_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/nftPlaceholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_off_d4865766.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_off_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_play-store_524f69a8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_play-store_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_plus_98f04ad6.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_plus_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_qr-code_0ecce46b.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_qr-code_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_recycle-horizontal_5cb5994c.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_recycle-horizontal_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/refresh.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_refresh_4cdb9f84.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_refresh_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/refresh.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/search.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_search_90efc7a7.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_search_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/search.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/send.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_send_804c4244.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_send_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/send.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontal_22c4e57e.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontal_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalMedium.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalMedium_c80fa3c6.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalMedium_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalMedium.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_baf7c314.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalRoundedBold.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalRoundedBold_7e1066dc.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalRoundedBold_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalRoundedBold.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapVertical.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapVertical_aa9daa25.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_swapVertical_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapVertical.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_telegram_e3085988.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_telegram_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_three-dots_41c83dd1.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_three-dots_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_twitch_cf429650.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_twitch_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_x_23151ab8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_x_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitterIcon.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_twitterIcon_4b493b0e.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_twitterIcon_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitterIcon.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_verify_051378a2.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_verify_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify-filled.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_verify-filled_c7549704.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_verify-filled_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify-filled.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_wallet_42db1d1b.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_wallet_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_walletconnect_d12cd3e8.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_walletconnect_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet-placeholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_wallet-placeholder_5d3ae89f.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_wallet-placeholder_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet-placeholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_warning-circle_68ae291e.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_warning-circle_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_info_7bd58c03.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_info_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_exclamation-triangle_4d5113af.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_exclamation-triangle_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_reown-logo_fde013b5.js",
  "static/chunks/f4fca_@reown_appkit-ui_dist_esm_src_assets_svg_reown-logo_29f4727f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit-ui@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8_e245c24d03deadbe32e3f5bcd748b6d6/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js [app-client] (ecmascript)");
    });
});
}}),
}]);