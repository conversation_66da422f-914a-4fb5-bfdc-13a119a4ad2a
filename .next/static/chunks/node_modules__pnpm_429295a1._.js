(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@noble+curves@1.8.1/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_2ced8c7a._.js",
  "static/chunks/27fbc_@noble_curves_esm_secp256k1_24d25972.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@noble+curves@1.8.1/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@reown+appkit@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8.3_utf-8-validate@5.0.10_zod@3.22.4/node_modules/@reown/appkit/dist/esm/exports/core.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/c4fa0_@reown_appkit-scaffold-ui_dist_esm_exports_c2ffc4e2._.js",
  "static/chunks/cdc4f_@walletconnect_utils_dist_index_es_7f8e0c8e.js",
  "static/chunks/97505_@walletconnect_core_dist_index_es_60f39b00.js",
  "static/chunks/00a68_@walletconnect_sign-client_dist_index_es_24acdcc7.js",
  "static/chunks/b8fbd_@reown_appkit-controllers_dist_esm_src_8ffb2364._.js",
  "static/chunks/8540e_@reown_appkit_dist_esm_e77a7bca._.js",
  "static/chunks/node_modules__pnpm_efddd695._.js",
  "static/chunks/8540e_@reown_appkit_dist_esm_exports_core_4f8d0c61.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@reown+appkit@1.7.8_@types+react@19.1.8_bufferutil@4.0.9_react@19.1.0_typescript@5.8.3_utf-8-validate@5.0.10_zod@3.22.4/node_modules/@reown/appkit/dist/esm/exports/core.js [app-client] (ecmascript)");
    });
});
}}),
}]);