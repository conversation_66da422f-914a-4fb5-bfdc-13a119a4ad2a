{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/poker/src/lib/wallet-config.ts"], "sourcesContent": ["'use client';\n\nimport { getDefaultConfig } from '@rainbow-me/rainbowkit';\nimport { avalanche, avalancheFuji } from 'wagmi/chains';\n\n// Configure the Avalanche chain for the wallet\nexport const config = getDefaultConfig({\n  appName: 'Royalty - Music Investment Platform',\n  projectId: process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || '2f5a2b1c8d3e4f5a6b7c8d9e0f1a2b3c',\n  chains: [avalanche, avalancheFuji],\n  ssr: false, // Disable SSR to avoid indexedDB issues\n});\n\n// Export chains for use in other components\nexport { avalanche, avalancheFuji };\n"], "names": [], "mappings": ";;;AAQa;AANb;AACA;AAAA;AAHA;;;AAMO,MAAM,SAAS,CAAA,GAAA,4XAAA,CAAA,mBAAgB,AAAD,EAAE;IACrC,SAAS;IACT,WAAW,wEAAqD;IAChE,QAAQ;QAAC,oUAAA,CAAA,YAAS;QAAE,wUAAA,CAAA,gBAAa;KAAC;IAClC,KAAK;AACP", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/poker/src/components/WalletProviders.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useState, useEffect } from 'react';\nimport { WagmiProvider } from 'wagmi';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { RainbowKitProvider, darkTheme } from '@rainbow-me/rainbowkit';\nimport { config } from '@/lib/wallet-config';\n\n// Import RainbowKit styles\nimport '@rainbow-me/rainbowkit/styles.css';\n\nconst queryClient = new QueryClient();\n\ninterface WalletProvidersProps {\n  children: ReactNode;\n}\n\nexport function WalletProviders({ children }: WalletProvidersProps) {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return <>{children}</>;\n  }\n\n  return (\n    <WagmiProvider config={config}>\n      <QueryClientProvider client={queryClient}>\n        <RainbowKitProvider\n          theme={darkTheme({\n            accentColor: '#FFC700',\n            accentColorForeground: '#1E293B',\n            borderRadius: 'medium',\n            fontStack: 'system',\n            overlayBlur: 'small',\n          })}\n          appInfo={{\n            appName: 'Royalty',\n            learnMoreUrl: 'https://rainbowkit.com',\n          }}\n        >\n          {children}\n        </RainbowKitProvider>\n      </QueryClientProvider>\n    </WagmiProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;AANA;;;;;;;AAWA,MAAM,cAAc,IAAI,yPAAA,CAAA,cAAW;AAM5B,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;qCAAE;YACR,WAAW;QACb;oCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBACE,4TAAC,kWAAA,CAAA,gBAAa;QAAC,QAAQ,iJAAA,CAAA,SAAM;kBAC3B,cAAA,4TAAC,yRAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC3B,cAAA,4TAAC,4XAAA,CAAA,qBAAkB;gBACjB,OAAO,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE;oBACf,aAAa;oBACb,uBAAuB;oBACvB,cAAc;oBACd,WAAW;oBACX,aAAa;gBACf;gBACA,SAAS;oBACP,SAAS;oBACT,cAAc;gBAChB;0BAEC;;;;;;;;;;;;;;;;AAKX;GAhCgB;KAAA", "debugId": null}}]}