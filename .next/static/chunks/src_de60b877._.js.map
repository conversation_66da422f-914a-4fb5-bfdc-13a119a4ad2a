{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/poker/src/contexts/WalletAuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, ReactNode } from 'react';\nimport { useAccount, useDisconnect } from 'wagmi';\n\ninterface WalletAuthContextType {\n  isConnected: boolean;\n  address: string | undefined;\n  isConnecting: boolean;\n  isReconnecting: boolean;\n  disconnect: () => void;\n}\n\nconst WalletAuthContext = createContext<WalletAuthContextType | undefined>(undefined);\n\ninterface WalletAuthProviderProps {\n  children: ReactNode;\n}\n\nexport function WalletAuthProvider({ children }: WalletAuthProviderProps) {\n  const { address, isConnected, isConnecting, isReconnecting } = useAccount();\n  const { disconnect } = useDisconnect();\n\n  const value: WalletAuthContextType = {\n    isConnected,\n    address,\n    isConnecting,\n    isReconnecting,\n    disconnect,\n  };\n\n  return (\n    <WalletAuthContext.Provider value={value}>\n      {children}\n    </WalletAuthContext.Provider>\n  );\n}\n\nexport function useWalletAuth() {\n  const context = useContext(WalletAuthContext);\n  if (context === undefined) {\n    throw new Error('useWalletAuth must be used within a WalletAuthProvider');\n  }\n  return context;\n}\n\n// Hook to check if user is authenticated (has connected wallet)\nexport function useIsAuthenticated() {\n  const { isConnected } = useWalletAuth();\n  return isConnected;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;;;AAHA;;;AAaA,MAAM,kCAAoB,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAAqC;AAMpE,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8WAAA,CAAA,aAAU,AAAD;IACxE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iXAAA,CAAA,gBAAa,AAAD;IAEnC,MAAM,QAA+B;QACnC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,4TAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;GAjBgB;;QACiD,8WAAA,CAAA,aAAU;QAClD,iXAAA,CAAA,gBAAa;;;KAFtB;AAmBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,OAAO;AACT;IAHgB;;QACU", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/poker/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { MusicalNoteIcon, ChartBarIcon, CurrencyDollarIcon, UserGroupIcon } from '@heroicons/react/24/solid';\nimport dynamic from 'next/dynamic';\nimport { useWalletAuth } from '@/contexts/WalletAuthContext';\n\nconst ConnectButton = dynamic(\n  () => import('@rainbow-me/rainbowkit').then((mod) => ({ default: mod.ConnectButton })),\n  { ssr: false }\n);\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const { isConnected } = useWalletAuth();\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: ChartBarIcon },\n    { name: 'Artists', href: '/artists', icon: MusicalNoteIcon },\n    { name: 'Marketplace', href: '/marketplace', icon: CurrencyDollarIcon },\n    { name: 'Staking', href: '/staking', icon: UserGroupIcon },\n  ];\n\n  return (\n    <nav className=\"bg-[#1E293B] border-b border-[#FFC700]/20 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-[#FFC700] rounded-lg flex items-center justify-center\">\n                <MusicalNoteIcon className=\"w-5 h-5 text-[#1E293B]\" />\n              </div>\n              <span className=\"text-xl font-bold text-[#FFC700]\">Royalty</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {isConnected && navigation.map((item) => {\n              const Icon = item.icon;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center space-x-1 text-gray-300 hover:text-[#FFC700] transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Connect Wallet Button */}\n          <div className=\"hidden md:flex items-center\">\n            <ConnectButton />\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-gray-300 hover:text-[#FFC700] transition-colors duration-200\"\n            >\n              {isOpen ? (\n                <XMarkIcon className=\"w-6 h-6\" />\n              ) : (\n                <Bars3Icon className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 bg-[#1E293B] border-t border-[#FFC700]/20\">\n            {isConnected && navigation.map((item) => {\n              const Icon = item.icon;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center space-x-2 text-gray-300 hover:text-[#FFC700] hover:bg-[#FFC700]/10 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n            <div className=\"pt-4 pb-2\">\n              <ConnectButton />\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;AAPA;;;;;;;AASA,MAAM,gBAAgB,CAAA,GAAA,iSAAA,CAAA,UAAO,AAAD,EAC1B,IAAM,wTAAiC,IAAI,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS,IAAI,aAAa;QAAC,CAAC;;;;;;IAClF,KAAK;;KAFH;AAKN,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEpC,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,+SAAA,CAAA,eAAY;QAAC;QAC5D;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,qTAAA,CAAA,kBAAe;QAAC;QAC3D;YAAE,MAAM;YAAe,MAAM;YAAgB,MAAM,2TAAA,CAAA,qBAAkB;QAAC;QACtE;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,iTAAA,CAAA,gBAAa;QAAC;KAC1D;IAED,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,8RAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC,qTAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,4TAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;;;;;;sCAKvD,4TAAC;4BAAI,WAAU;sCACZ,eAAe,WAAW,GAAG,CAAC,CAAC;gCAC9B,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,4TAAC,8RAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,4TAAC;4CAAK,WAAU;;;;;;sDAChB,4TAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;4BAQpB;;;;;;sCAIF,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;;;;;;;;;;sCAIH,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBACC,4TAAC,2SAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,4TAAC,2SAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9B,wBACC,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;wBACZ,eAAe,WAAW,GAAG,CAAC,CAAC;4BAC9B,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,4TAAC,8RAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,4TAAC;wCAAK,WAAU;;;;;;kDAChB,4TAAC;kDAAM,KAAK,IAAI;;;;;;;+BANX,KAAK,IAAI;;;;;wBASpB;sCACA,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAzFM;;QAEoB,wIAAA,CAAA,gBAAa;;;MAFjC;uCA2FS", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/poker/src/components/ClientWalletWrapper.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport dynamic from 'next/dynamic';\n\nconst WalletProviders = dynamic(\n  () => import(\"@/components/WalletProviders\").then((mod) => ({ default: mod.WalletProviders })),\n  { ssr: false }\n);\n\nconst WalletAuthProvider = dynamic(\n  () => import(\"@/contexts/WalletAuthContext\").then((mod) => ({ default: mod.WalletAuthProvider })),\n  { ssr: false }\n);\n\ninterface ClientWalletWrapperProps {\n  children: ReactNode;\n}\n\nexport function ClientWalletWrapper({ children }: ClientWalletWrapperProps) {\n  return (\n    <WalletProviders>\n      <WalletAuthProvider>\n        {children}\n      </WalletAuthProvider>\n    </WalletProviders>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;;;AAHA;;;AAKA,MAAM,kBAAkB,CAAA,GAAA,iSAAA,CAAA,UAAO,AAAD,EAC5B,IAAM,6JAAuC,IAAI,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS,IAAI,eAAe;QAAC,CAAC;;;;;;IAC1F,KAAK;;KAFH;AAKN,MAAM,qBAAqB,CAAA,GAAA,iSAAA,CAAA,UAAO,AAAD,EAC/B,IAAM,6JAAuC,IAAI,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS,IAAI,kBAAkB;QAAC,CAAC;;;;;;IAC7F,KAAK;;MAFH;AASC,SAAS,oBAAoB,EAAE,QAAQ,EAA4B;IACxE,qBACE,4TAAC;kBACC,cAAA,4TAAC;sBACE;;;;;;;;;;;AAIT;MARgB", "debugId": null}}]}